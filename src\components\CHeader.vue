<!-- 顶部标题 -->
<template>
  <header class="header"></header>
  <!-- 统计卡片 -->
  <div class="stats-container">
    <div class="card-item" v-for="item in statsData" :key="item.title">
      <div class="card-icon">
        <img :src="item.icon" :alt="item.title" />
      </div>
      <div class="card-content">
        <div class="card-number">{{ item.number }}</div>
        <div class="card-title">{{ item.title }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义数据类型
interface StatsItem {
  icon: string
  number: string
  title: string
}

// 统计数据
const statsData: StatsItem[] = [
  {
    icon: new URL('@/assets/img/牛.png', import.meta.url).href,
    number: '526',
    title: '散养户🐄'
  },
  {
    icon: new URL('@/assets/img/猪.png', import.meta.url).href,
    number: '84',
    title: '家庭农场🐷'
  },
  {
    icon: new URL('@/assets/img/羊.png', import.meta.url).href,
    number: '23',
    title: '规模场🐑'
  }
]
</script>

<style lang="scss" scoped>
.header {
  position: absolute;
  margin: 0 12px;
  top: 12px;
  width: calc(100% - 24px);
  height: 87px;
  background: url('@/assets/img/headBg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  font-weight: 800;
  letter-spacing: 12px;
  color: rgba(230, 239, 253);
}

.stats-container {
  position: absolute;
  top: 120px;
  left: 50%;
  width: 900px;
  height: 120px;
  transform: translateX(-50%);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px;
  box-sizing: border-box;
  pointer-events: all;
}

.card-item {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(0, 255, 255, 0.05));
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  padding: 15px 20px;
  min-width: 200px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 255, 255, 0.3);
    border-color: rgba(0, 255, 255, 0.5);
  }
}

.card-icon {
  width: 60px;
  height: 60px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 255, 255, 0.1));
  border-radius: 50%;

  img {
    width: 40px;
    height: 40px;
    object-fit: contain;
  }
}

.card-content {
  flex: 1;
}

.card-number {
  font-size: 32px;
  font-weight: bold;
  color: #00ffff;
  line-height: 1;
  margin-bottom: 5px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.card-title {
  font-size: 16px;
  color: #ffffff;
  opacity: 0.9;
  line-height: 1;
}


</style>
