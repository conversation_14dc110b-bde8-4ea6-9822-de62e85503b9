<!-- 底部组件 -->
<template>
  <footer class="table-footer">
    <!-- 左侧表格 - 任务推送 -->
    <div class="table-container">
      <div class="table-header">
        <div class="header-icon">
          <div class="glow-dot"></div>
        </div>
        <span class="header-title">任务推送</span>
      </div>
      <div class="table-content">
        <table class="data-table">
          <thead>
            <tr>
              <th>日期</th>
              <th>任务内容</th>
              <th>养殖场</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in taskData" :key="item.id">
              <td>{{ item.date }}</td>
              <td>{{ item.content }}</td>
              <td>{{ item.farm }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 右侧表格 - 生产管理 -->
    <div class="table-container">
      <div class="table-header">
        <div class="header-icon">
          <div class="glow-dot"></div>
        </div>
        <span class="header-title">生产管理</span>
      </div>
      <div class="table-content">
        <table class="data-table">
          <thead>
            <tr>
              <th>日期</th>
              <th>生产事件</th>
              <th>养殖场</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in productionData" :key="item.id">
              <td>{{ item.date }}</td>
              <td>{{ item.event }}</td>
              <td>{{ item.farm }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'

// 任务推送数据
const taskData = ref([
  {
    id: 1,
    date: '2025-8-28 19:18:46',
    content: '待查种30头',
    farm: '张三养殖场'
  },
  {
    id: 2,
    date: '2025-8-28 19:18:50',
    content: '待查种30头',
    farm: '张三养殖场'
  },
  {
    id: 3,
    date: '2025-8-28 19:18:50',
    content: '待查种30头',
    farm: '张三养殖场'
  }
])

// 生产管理数据
const productionData = ref([
  {
    id: 1,
    date: '2025-8-28 19:19:44',
    event: '配种',
    farm: '张三养殖场'
  },
  {
    id: 2,
    date: '2025-8-28 19:19:44',
    event: '配种',
    farm: '张三养殖场'
  },
  {
    id: 3,
    date: '2025-8-28 19:19:44',
    event: '配种',
    farm: '张三养殖场'
  }
])

let intervalId: any = null

// 模拟数据更新
function updateTableData() {
  // 更新任务推送数据
  taskData.value = taskData.value.map(item => ({
    ...item,
    date: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/\//g, '-')
  }))

  // 更新生产管理数据
  productionData.value = productionData.value.map(item => ({
    ...item,
    date: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/\//g, '-')
  }))
}

onMounted(() => {
  intervalId = window.setInterval(() => {
    updateTableData()
  }, 10000)
})

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})
</script>

<style lang="scss" scoped>
.table-footer {
  position: absolute;
  width: 100%;
  bottom: 24px;
  display: flex;
  justify-content: center;
  gap: 72px;
  pointer-events: none;
  bottom: -200px;
  animation: entranceAnimation ease-in-out 0.75s forwards;
}

.table-container {
  position: relative;
  width: 400px;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  background: rgba(0, 255, 255, 0.03);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 8px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
}

.table-header {
  height: 40px;
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.15) 0%, rgba(0, 255, 255, 0.08) 100%);
  border-bottom: 1px solid rgba(0, 255, 255, 0.3);
  display: flex;
  align-items: center;
  padding: 0 16px;
  gap: 12px;

  .header-icon {
    .glow-dot {
      width: 10px;
      height: 10px;
      background: #00FF88;
      border-radius: 50%;
      box-shadow: 0 0 6px #00FF88, 0 0 12px #00FF88;
      animation: pulse 2s infinite;
    }
  }

  .header-title {
    font-size: 16px;
    color: #FFFFFF;
    font-weight: 600;
    letter-spacing: 1px;
  }
}

.table-content {
  flex: 1;
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;

  thead {
    background: rgba(0, 255, 255, 0.08);

    th {
      padding: 12px 16px;
      text-align: center;
      color: #00FFFF;
      font-weight: 600;
      border-bottom: 1px solid rgba(0, 255, 255, 0.2);
      font-size: 14px;
      letter-spacing: 0.5px;
    }
  }

  tbody {
    tr {
      transition: all 0.3s ease;
      border-bottom: 1px solid rgba(0, 255, 255, 0.1);

      &:hover {
        background: rgba(0, 255, 255, 0.05);
      }

      &:last-child {
        border-bottom: none;
      }

      td {
        padding: 10px 16px;
        text-align: center;
        color: #FFFFFF;
        font-size: 13px;
        line-height: 1.4;

        &:first-child {
          color: #C5D6E6;
          font-size: 12px;
        }

        &:nth-child(2) {
          color: #00FF88;
          font-weight: 500;
        }

        &:last-child {
          color: #FFFFFF;
        }
      }
    }
  }
}

@keyframes entranceAnimation {
  0% {
    bottom: -200px;
  }
  100% {
    bottom: 24px;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}
</style>
