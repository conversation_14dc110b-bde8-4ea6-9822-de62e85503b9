<!-- 景区排名 -->
<template>
  <CPanel>
    <template #header>景区排名</template>
    <template #content>
      <div class="content">
        <div class="left-section">
          <div class="center-circle">
            <div class="total-number">2563</div>
            <div class="total-label">访问总人次</div>
          </div>
          <div class="chart-container" id="ranking-chart"></div>
        </div>
        <div class="right-section">
          <div class="legend-container">
            <div class="legend-column">
              <div class="legend-item" v-for="(item, index) in leftColumn" :key="index">
                <div class="color-box" :style="{ backgroundColor: item.color }"></div>
                <span class="legend-text">{{ item.name }} {{ item.percent }}%</span>
              </div>
            </div>
            <div class="legend-column">
              <div class="legend-item" v-for="(item, index) in rightColumn" :key="index">
                <div class="color-box" :style="{ backgroundColor: item.color }"></div>
                <span class="legend-text">{{ item.name }} {{ item.percent }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'

const statsList = ref([
  { name: '主题1', percent: 34, color: '#E83F46' },
  { name: '主题2', percent: 30, color: '#FF8D39' },
  { name: '主题3', percent: 34, color: '#FFD339' },
  { name: '主题4', percent: 30, color: '#B4E61A' },
  { name: '主题5', percent: 34, color: '#0DC869' },
  { name: '主题6', percent: 30, color: '#00D4DC' },
  { name: '主题7', percent: 34, color: '#4F7DFF' },
  { name: '主题8', percent: 30, color: '#AF48FF' }
])

// 将数据分为左右两列
const leftColumn = computed(() => statsList.value.slice(0, 4))
const rightColumn = computed(() => statsList.value.slice(4, 8))

const initChart = () => {
  const chartDom = document.getElementById('ranking-chart')
  if (chartDom) {
    // 清除之前的图表实例
    echarts.dispose(chartDom)
    const myChart = echarts.init(chartDom)
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          return params.seriesName + "</br>" +
            params.marker + "" + params.data.name + "</br>" +
            "数量：" + params.data.value + "</br>" +
            "占比：" + params.percent + "%"
        }
      },
      legend: {
        show: false // 隐藏默认图例，使用自定义图例
      },
      series: [{
        name: '景区排名',
        type: 'pie',
        radius: ['30%', '65%'],
        center: ['32%', '50%'],
        clockwise: false,
        avoidLabelOverlap: false,
        data: statsList.value.map(item => ({
          value: item.percent,
          name: item.name,
          itemStyle: { color: item.color }
        })),
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    myChart.setOption(option, true) // 强制重新渲染
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss" scoped>
.content {
  height: 180px;
  position: relative;
  padding: 10px;
  display: flex;
  align-items: center;
}

.left-section {
  position: relative;
  width: 60%;
  height: 100%;

  .center-circle {
    position: absolute;
    z-index: 2;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(0, 212, 220, 0.3);

    .total-number {
      font-size: 28px;
      font-weight: bold;
      color: #fff;
      line-height: 1;
      margin-bottom: 4px;
    }

    .total-label {
      font-size: 10px;
      color: rgba(255, 255, 255, 0.8);
      line-height: 1;
    }
  }
}

.chart-container {
  width: 100%;
  height: 100%;
}

.right-section {
  width: 40%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-container {
  display: flex;
  gap: 20px;
  height: 100%;
  align-items: center;
}

.legend-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-box {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
}
</style>
