<!-- 左侧数据面板 -->
<template>
  <div class="left-panel">
    <!-- 景点人流排名 -->
    <rankingOfScenicSpots />
    <!-- 游客年龄分布 -->
    <ageDistribution />
    <!-- 年度接待游客比 -->
    <receptionOfTourists />
    <!-- 配方生成 -->
    <formulaGeneration />
  </div>
</template>

<script setup lang="ts">
import rankingOfScenicSpots from './leftPanel/rankingOfScenicSpots.vue'
import ageDistribution from './leftPanel/ageDistribution.vue'
import receptionOfTourists from './leftPanel/receptionOfTourists.vue'
import formulaGeneration from './leftPanel/formulaGeneration.vue'
</script>

<style lang="scss" scoped>
.left-panel {


  position: absolute;
  width: 500px;
  height: 100%;
  left: 0;
  top: 0;
  display: grid;
  gap: 24px;
  padding: 85px 0 24px 0;
  box-sizing: border-box;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(4, 1fr);
}

.panel {
  left: -500px;
  animation: entranceAnimation ease-in-out 0.75s forwards;
}

@keyframes entranceAnimation {
  0% {
    left: -500px;
  }

  100% {
    left: 0;
  }
}
</style>
