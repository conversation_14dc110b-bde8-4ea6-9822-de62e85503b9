<!-- 山东实时热词  -->
<template>
  <CPanel>
    <template #header>山东实时热词</template>
    <template #content>
      <div class="content-container">
        <!-- 左边仪表盘 -->
        <div class="gauge-section">
          <CEcharts :option="gaugeOption" />
        </div>
        <!-- 右边数据列表 -->
        <div class="list-section">
          <div class="list-item" v-for="(item, index) in listData" :key="index">
            <div class="bullet"></div>
            <div class="item-content">
              <span class="item-name">{{ item.name }}</span>
              <span class="item-value">{{ item.value }} 头</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const gaugeOption = ref<any>({})
const listData = ref([
  { name: '疫病名称1', value: 256 },
  { name: '疫病名称2', value: 137 },
  { name: '疫病名称3', value: 87 },
  { name: '疫病名称4', value: 56 },
  { name: '疫病名称5', value: 24 }
])

const initGaugeChart = () => {
  const uploadedDataURL = "/asset/get/s/data-1559120606810-8K55q2kWT.png";
  const Green = {
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [{
      offset: 0,
      color: '#99da69' // 0% 处的颜色
    }, {
      offset: 1,
      color: '#01babc' // 100% 处的颜色
    }],
    globalCoord: false // 缺省为 false
  };
  const dataValArray = 0.63;
  const datatext = ['17.25','20','12','32','19'];
  const datasubtext = ['体育意识','体育技能','体育行为','体育知识','体质健康'];
  
  const options = {
    backgroundColor: '#fff',
    title: {
      text: datatext[0]+'%',
      subtext: datasubtext[0],
      x: 'center',
      y: 'center',
      textStyle: {
        fontSize: 26,
        fontWeight: 'normal',
        color: ['#67828c']
      },
      subtextStyle: {
        color: '#67828c',
        fontSize: 16,
        align: 'center',
      }
    },
    series: [{
      //渐变圆环
      name: "",
      type: "pie",
      radius: ["35%", "50%"],
      startAngle: 180,
      hoverAnimation: false,
      avoidLabelOverlap: true,
      z: 0,
      zlevel: 0,
      label: {
        show: false,
        normal: {show: false}
      },
      data: [{
        value: 0,
        name: "",
        itemStyle: {
          normal: {
            color: Green
          }
        }
      }]
    },
    {
      //仪表盘样式
      name: "",
      type: "gauge",
      radius: "50%",
      startAngle: 180,
      clockwise: true,
      splitNumber: 50,
      hoverAnimation: true,
      axisTick: {
        show: false
      },
      splitLine: {
        length: 20,
        lineStyle: {
          width: 1,
          color: "#fff"
        }
      },
      axisLabel: {
        show: false
      },
      pointer: {
        show: false
      },
      axisLine: {
        lineStyle: {
          opacity: 0
        }
      },
      detail: {
        show: false
      },
      data: [{
        value: Math.round((dataValArray*100)),
        name: ""
      }]
    },
    {
      //进度圆环
      name: 'Line 1',
      type: 'pie',
      startAngle: 180,
      clockWise: true,
      radius: ['55%','56%'],
      itemStyle: {
        normal: {
          label: {
            show: true
          },
          labelLine: {
            show: false
          },
        }
      },
      hoverAnimation: false,
      data: [{
        value: Math.round((dataValArray*100)),
        itemStyle: {
          normal: {
            color: '#20da97'
          }
        }
      },{//画中间的图标
        name: "",
        value: 0,
        label: {
          position: 'inside',
          backgroundColor: {
            image: uploadedDataURL
          },
          width: 16,
          height: 16,
          borderRadius: 20,
          padding: 11
        }
      }, {
        value: 100- Math.round((dataValArray*100)),
        name: 'invisible',
        itemStyle: {
          normal: {
            color: 'transparent', //未完成的圆环的颜色
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          }
        }
      }]
    }]
  };
  return options
}

onMounted(() => {
  gaugeOption.value = initGaugeChart()
})
</script>

<style lang="scss" scoped>
::v-deep .panel-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-container {
  display: flex;
  width: 100%;
  height: 200px;
  gap: 20px;
  padding: 0 12px 14px 12px;
}

.gauge-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 12px;
}

.list-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bullet {
  width: 8px;
  height: 8px;
  background: #58D9F8;
  border-radius: 2px;
  flex-shrink: 0;
}

.item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.item-name {
  color: #fff;
  font-size: 14px;
}

.item-value {
  color: #00FF88;
  font-size: 16px;
  font-weight: 500;
}
</style>
