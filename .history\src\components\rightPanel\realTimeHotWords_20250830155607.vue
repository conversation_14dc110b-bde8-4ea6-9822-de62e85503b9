<!-- 山东实时热词  -->
<template>
  <CPanel>
    <template #header>山东实时热词</template>
    <template #content>
      <div class="content-container">
        <!-- 左边仪表盘 -->
        <div class="gauge-section">
          <CEcharts :option="gaugeOption" />
        </div>
        <!-- 右边数据列表 -->
        <div class="list-section">
          <div class="list-item" v-for="(item, index) in listData" :key="index">
            <div class="bullet"></div>
            <div class="item-content">
              <span class="item-name">{{ item.name }}</span>
              <span class="item-value">{{ item.value }} 头</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const gaugeOption = ref<any>({})
const listData = ref([
  { name: '疫病名称1', value: 256 },
  { name: '疫病名称2', value: 137 },
  { name: '疫病名称3', value: 87 },
  { name: '疫病名称4', value: 56 },
  { name: '疫病名称5', value: 24 }
])

const initGaugeChart = () => {
  const options = {
    series: [
      {
        type: 'gauge',
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max: 100,
        splitNumber: 10,
        itemStyle: {
          color: '#58D9F8',
          shadowColor: 'rgba(0, 138, 255, 0.3)',
          shadowBlur: 8,
          shadowOffsetX: 0,
          shadowOffsetY: 0
        },
        progress: {
          show: true,
          roundCap: true,
          width: 18,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#58D9F8' },
                { offset: 1, color: '#00FF88' }
              ]
            }
          }
        },
        pointer: {
          icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
          length: '75%',
          width: 16,
          offsetCenter: [0, '5%']
        },
        axisLine: {
          roundCap: true,
          lineStyle: {
            width: 18
          }
        },
        axisTick: {
          splitNumber: 2,
          lineStyle: {
            width: 2,
            color: '#999'
          }
        },
        splitLine: {
          length: 12,
          lineStyle: {
            width: 3,
            color: '#999'
          }
        },
        axisLabel: {
          distance: 30,
          color: '#999',
          fontSize: 12
        },
        title: {
          offsetCenter: [0, '30%'],
          fontSize: 20,
          color: '#fff'
        },
        detail: {
          fontSize: 30,
          offsetCenter: [0, '0%'],
          valueAnimation: true,
          formatter: function (value: number) {
            return Math.round(value) + '%'
          },
          color: '#fff'
        },
        data: [
          {
            value: 92.5,
            name: '防疫落实'
          }
        ]
      }
    ]
  }
  return options
}

onMounted(() => {
  gaugeOption.value = initGaugeChart()
})
</script>

<style lang="scss" scoped>
::v-deep .panel-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-container {
  display: flex;
  width: 100%;
  height: 200px;
  gap: 20px;
  padding: 0 12px 14px 12px;
}

.gauge-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 12px;
}

.list-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bullet {
  width: 8px;
  height: 8px;
  background: #58D9F8;
  border-radius: 2px;
  flex-shrink: 0;
}

.item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.item-name {
  color: #fff;
  font-size: 14px;
}

.item-value {
  color: #00FF88;
  font-size: 16px;
  font-weight: 500;
}
</style>
