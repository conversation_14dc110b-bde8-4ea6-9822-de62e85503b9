<!-- 农场信息悬浮提示 -->
<template>
  <div
    v-if="visible"
    class="tooltip-container"
    :style="{ left: position.x + 'px', top: position.y + 'px' }"
  >
    <!-- 悬浮提示框 -->
    <div class="tooltip-content">
      <!-- 标题 -->
      <div class="tooltip-header">
        <div class="tooltip-title">{{ farmData?.name || '养殖场信息' }}</div>
        <div class="tooltip-subtitle">(存栏{{ farmData?.value || 0 }}头)</div>
      </div>

      <!-- 地址信息 -->
      <div class="tooltip-address">
        <span class="address-label">地址：</span>
        <span class="address-text">{{ farmData?.address || '暂无地址信息' }}</span>
      </div>

      <!-- 详细记录 -->
      <div class="tooltip-details">
        <div v-for="(detail, index) in farmData?.details || []" :key="index" class="detail-item">
          <span class="detail-time">• {{ detail.time }}</span>
          <span class="detail-level">{{ detail.level }}</span>
        </div>
      </div>
    </div>

    <!-- 箭头指示器 -->
    <div class="tooltip-arrow"></div>
  </div>
</template>

<script setup lang="ts">
interface FarmDetail {
  time: string
  level: string
}

interface FarmData {
  name: string
  address: string
  details: FarmDetail[]
  image?: string
  value: number
}

interface Position {
  x: number
  y: number
}

const props = defineProps<{
  visible: boolean
  farmData: FarmData | null
  position: Position
}>()
</script>

<style lang="scss" scoped>
.tooltip-container {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tooltip-content {
  position: relative;
  width: 320px;
  background: linear-gradient(135deg,
    rgba(0, 30, 40, 0.95) 0%,
    rgba(0, 50, 60, 0.95) 50%,
    rgba(0, 70, 80, 0.95) 100%);
  border: 2px solid #00CED1;
  border-radius: 8px;
  padding: 16px;
  box-shadow:
    0 0 20px rgba(0, 206, 209, 0.6),
    0 0 40px rgba(0, 206, 209, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transform: translateY(-10px);
}

.tooltip-header {
  margin-bottom: 12px;
  border-bottom: 1px solid rgba(0, 206, 209, 0.3);
  padding-bottom: 8px;
}

.tooltip-title {
  font-size: 18px;
  font-weight: bold;
  color: #00CED1;
  margin-bottom: 4px;
  text-shadow: 0 0 8px rgba(0, 206, 209, 0.5);
}

.tooltip-subtitle {
  font-size: 14px;
  color: #7DD3FC;
  opacity: 0.9;
}

.tooltip-address {
  margin-bottom: 12px;
  font-size: 13px;
  line-height: 1.4;
}

.address-label {
  color: #00CED1;
  font-weight: 500;
}

.address-text {
  color: #E6F7FF;
}

.tooltip-details {
  max-height: 120px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 206, 209, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 206, 209, 0.5);
    border-radius: 2px;
  }
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  margin-bottom: 4px;
  background: rgba(0, 206, 209, 0.08);
  border-radius: 4px;
  border-left: 2px solid #00CED1;
  font-size: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-time {
  color: #B8E6E6;
  flex: 1;
}

.detail-level {
  color: #00CED1;
  font-weight: 500;
  padding: 2px 8px;
  background: rgba(0, 206, 209, 0.15);
  border-radius: 8px;
  border: 1px solid rgba(0, 206, 209, 0.3);
}

.tooltip-arrow {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #00CED1;

  &::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -6px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid rgba(0, 50, 60, 0.95);
  }
}
</style>
