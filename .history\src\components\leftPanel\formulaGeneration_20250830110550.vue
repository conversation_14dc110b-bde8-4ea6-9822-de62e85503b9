<!-- 配方生成 -->
<template>
  <CPanel>
    <template #header>配方生成</template>
    <template #content>
      <div class="formula-container">
        <!-- 左侧配方列表 -->
        <div class="formula-list">
          <div 
            class="formula-item" 
            v-for="(item, index) in formulaList" 
            :key="index"
            :class="{ active: index === activeIndex }"
          >
            <div class="formula-icon">
              <div class="glow-dot"></div>
            </div>
            <div class="formula-info">
              <div class="formula-name">{{ item.name }}</div>
              <div class="formula-time">{{ item.farm }} | {{ item.time }}</div>
            </div>
          </div>
        </div>
        
        <!-- 右侧环形图 -->
        <div class="chart-section">
          <div class="chart-container" ref="chartRef"></div>
          <div class="chart-legend">
            <div class="legend-item" v-for="legend in legendData" :key="legend.name">
              <div class="legend-color" :style="{ backgroundColor: legend.color }"></div>
              <span class="legend-text">{{ legend.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'

const chartRef = ref<HTMLElement>()
let myChart: echarts.ECharts | null = null
let scrollTimer: number | null = null

const formulaList = ref([
  { name: '母牛配方', farm: '张三养殖场', time: '2025-8-28 19:17:36' },
  { name: '母牛配方', farm: '张三养殖场', time: '2025-8-28 19:17:36' },
  { name: '母牛配方', farm: '张三养殖场', time: '2025-8-28 19:17:36' },
  { name: '母牛配方', farm: '张三养殖场', time: '2025-8-28 19:17:36' }
])

const legendData = [
  { name: '育肥牛', color: '#00D4FF' },
  { name: '后备母牛', color: '#FFB800' },
  { name: '妊娠母牛', color: '#00FF88' },
  { name: '哺乳母牛', color: '#00FFFF' }
]

const activeIndex = ref(0)

// 自动滚动配方列表
const startAutoScroll = () => {
  scrollTimer = window.setInterval(() => {
    activeIndex.value = (activeIndex.value + 1) % formulaList.value.length
  }, 3000)
}

// 初始化环形图
const initChart = () => {
  if (!chartRef.value) return
  
  myChart = echarts.init(chartRef.value)
  const option = {
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      data: [
        { value: 25, name: '育肥牛', itemStyle: { color: '#00D4FF' } },
        { value: 25, name: '后备母牛', itemStyle: { color: '#FFB800' } },
        { value: 25, name: '妊娠母牛', itemStyle: { color: '#00FF88' } },
        { value: 25, name: '哺乳母牛', itemStyle: { color: '#00FFFF' } }
      ],
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
      emphasis: {
        scale: false
      }
    }]
  }
  myChart.setOption(option)
}

onMounted(() => {
  nextTick(() => {
    initChart()
    startAutoScroll()
  })
})

onUnmounted(() => {
  if (scrollTimer) {
    clearInterval(scrollTimer)
  }
  if (myChart) {
    myChart.dispose()
  }
})
</script>

<style lang="scss" scoped>
.formula-container {
  display: flex;
  height: 280px;
  gap: 20px;
}

.formula-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
  padding-right: 8px;

  .formula-item {
    display: flex;
    align-items: center;
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px;
    transition: all 0.3s ease;
    cursor: pointer;

    &.active {
      border: 2px dashed rgba(0, 255, 255, 0.6);
      background: rgba(0, 255, 255, 0.1);
      transform: translateX(4px);
    }

    .formula-icon {
      margin-right: 12px;
      
      .glow-dot {
        width: 12px;
        height: 12px;
        background: #00FF88;
        border-radius: 50%;
        box-shadow: 0 0 8px #00FF88, 0 0 16px #00FF88;
        animation: pulse 2s infinite;
      }
    }

    .formula-info {
      flex: 1;

      .formula-name {
        font-size: 16px;
        color: #FFFFFF;
        font-weight: 600;
        margin-bottom: 6px;
        line-height: 1.2;
      }

      .formula-time {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.2;
      }
    }
  }
}

.chart-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;

  .chart-container {
    width: 100px;
    height: 100px;
  }

  .chart-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 4px;
      }

      .legend-text {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
        white-space: nowrap;
      }
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 8px #00FF88, 0 0 16px #00FF88;
  }
  50% {
    box-shadow: 0 0 12px #00FF88, 0 0 24px #00FF88;
  }
  100% {
    box-shadow: 0 0 8px #00FF88, 0 0 16px #00FF88;
  }
}

// 自定义滚动条样式
.formula-list::-webkit-scrollbar {
  width: 4px;
}

.formula-list::-webkit-scrollbar-track {
  background: rgba(0, 255, 255, 0.1);
  border-radius: 2px;
}

.formula-list::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 2px;
}

.formula-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 255, 0.5);
}
</style>
