<!-- 行情咨询 -->
<template>
  <CPanel>
    <template #header>行情咨询</template>
    <template #content>
      <div class="market-cards">
        <div class="market-card">
          <div class="card-icon">
            <div class="icon-glow">
              <div class="icon-stack"></div>
            </div>
          </div>
          <div class="card-label">活牛价格</div>
          <div class="card-value">4256</div>
        </div>
        
        <div class="market-card">
          <div class="card-icon">
            <div class="icon-glow">
              <div class="icon-bag"></div>
            </div>
          </div>
          <div class="card-label">饲料原料</div>
          <div class="card-value">2250</div>
        </div>
        
        <div class="market-card">
          <div class="card-icon">
            <div class="icon-glow">
              <div class="icon-globe"></div>
            </div>
          </div>
          <div class="card-label">农业新闻</div>
          <div class="card-value">852</div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import CPanel from '@/components/common/CPanel.vue'
</script>

<style lang="scss" scoped>
.market-cards {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  gap: 16px;
}

.market-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  position: relative;
  
  .card-icon {
    margin-bottom: 8px;
    
         .icon-glow {
       position: relative;
       width: 60px;
       height: 60px;
       display: flex;
       align-items: center;
       justify-content: center;
       
       &::before {
         content: '';
         position: absolute;
         top: 0;
         left: 0;
         right: 0;
         bottom: 0;
         border-radius: 50%;
         background: radial-gradient(circle, rgba(0, 221, 255, 0.6) 0%, rgba(0, 221, 255, 0.3) 50%, transparent 70%);
         filter: blur(8px);
         z-index: 0;
       }
     }
     
     .icon-stack {
       width: 24px;
       height: 28px;
       background: #00DDFF;
       border-radius: 2px;
       position: relative;
       z-index: 1;
       
       &::before,
       &::after {
         content: '';
         position: absolute;
         width: 24px;
         height: 28px;
         background: #00DDFF;
         border-radius: 2px;
       }
       
       &::before {
         top: -4px;
         left: 2px;
         opacity: 0.8;
       }
       
       &::after {
         top: -8px;
         left: 4px;
         opacity: 0.6;
       }
     }
     
     .icon-bag {
       width: 28px;
       height: 24px;
       background: #00DDFF;
       border-radius: 14px 14px 4px 4px;
       position: relative;
       z-index: 1;
       
       &::before {
         content: '';
         position: absolute;
         top: -2px;
         left: 50%;
         transform: translateX(-50%);
         width: 10px;
         height: 4px;
         background: #00DDFF;
         border-radius: 5px 5px 0 0;
       }
     }
     
     .icon-globe {
       width: 28px;
       height: 28px;
       border: 2px solid #00DDFF;
       border-radius: 50%;
       position: relative;
       z-index: 1;
       
       &::before {
         content: '';
         position: absolute;
         top: 50%;
         left: 50%;
         transform: translate(-50%, -50%);
         width: 18px;
         height: 18px;
         border: 1px solid #00DDFF;
         border-radius: 50%;
       }
       
       &::after {
         content: '';
         position: absolute;
         top: 50%;
         left: 0;
         right: 0;
         height: 1px;
         background: #00DDFF;
       }
     }
  }
  
  .card-label {
    font-size: 14px;
    color: #C5D6E6;
    margin-bottom: 8px;
    text-align: center;
  }
  
  .card-value {
    font-size: 24px;
    font-weight: bold;
    color: #FFFFFF;
    text-align: center;
  }
}

// 小背景框的颜色样式
.market-card {
  background: linear-gradient(135deg, #1F786A 0%, #006265 100%);
  border-radius: 8px;
  padding: 16px 12px;
  box-shadow: 0 4px 20px rgba(0, 98, 101, 0.3);
  border: 0.5pt solid #006265;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 8px;
    background: linear-gradient(135deg, #1F786A 0%, #006265 100%);
    opacity: 0.1;
    z-index: -1;
  }
}
</style>
