<!-- 技术课堂 -->
<template>
  <CPanel>
    <template #header>存出栏</template>
    <template #content>
      <div class="content">
        <div class="chart-container" id="technical-chart"></div>
        
        <!-- 数据面板 -->
        <div class="data-panels">
          <!-- 不同主体存栏 -->
          <div class="panel-item">
            <div class="panel-left">
              <div class="icon-container">
                <img src="@/assets/img/不同主体同栏.png" alt="不同主体存栏" class="panel-icon">
              </div>
              <div class="panel-label">不同主体存栏</div>
            </div>
            <div class="panel-right">
              <div class="data-item">
                <div class="data-value">412</div>
                <div class="data-label">散养户</div>
              </div>
              <div class="data-item">
                <div class="data-value">412</div>
                <div class="data-label">家庭农场</div>
              </div>
              <div class="data-item">
                <div class="data-value">412</div>
                <div class="data-label">规模场</div>
              </div>
            </div>
          </div>
          
          <!-- 不同类型存栏 -->
          <div class="panel-item">
            <div class="panel-left">
              <div class="icon-container">
                <img src="@/assets/img/不同类型存栏.png" alt="不同类型存栏" class="panel-icon">
              </div>
              <div class="panel-label">不同类型存栏</div>
            </div>
            <div class="panel-right">
              <div class="data-item">
                <div class="data-value">412</div>
                <div class="data-label">母牛</div>
              </div>
              <div class="data-item">
                <div class="data-value">412</div>
                <div class="data-label">犊牛</div>
              </div>
              <div class="data-item">
                <div class="data-value">412</div>
                <div class="data-label">育肥牛</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'

const initChart = () => {
  const chartDom = document.getElementById('technical-chart')
  if (chartDom) {
    const myChart = echarts.init(chartDom)
    const option = {
      backgroundColor: 'transparent',
      grid: {
        left: '0',
        right: '0',
        top: '25%',
        bottom: '0',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月'],
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 2500,
        interval: 500,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 12
        }
      },
      legend: {
        top: '5%',
        left: 'center',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: 12
        },
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 20
      },
      series: [
        {
          name: '总存栏',
          type: 'line',
          data: [1100, 1050, 1600, 2000, 1700, 1450, 1400, 1900, 1950],
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            color: '#2EFFD4',
            width: 2
          },
          itemStyle: {
            color: '#2EFFD4'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(46, 255, 212, 0.3)' },
                { offset: 1, color: 'rgba(46, 255, 212, 0.05)' }
              ]
            }
          }
        },
        {
          name: '出栏数',
          type: 'line',
          data: [700, 850, 1200, 1350, 1000, 900, 1000, 1250, 1000],
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            color: '#FFC85F',
            width: 2
          },
          itemStyle: {
            color: '#FFC85F'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(255, 200, 95, 0.3)' },
                { offset: 1, color: 'rgba(255, 200, 95, 0.05)' }
              ]
            }
          }
        },
        {
          name: '预计年度出栏',
          type: 'line',
          data: [2300, 2300, 2300, 2300, 2300, 2300, 2300, 2300, 2300],
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            color: '#006265',
            width: 2
          },
          itemStyle: {
            color: '#006265'
          }
        }
      ]
    }
    myChart.setOption(option)
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss" scoped>
.content {
  height: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  padding: 10px 0;
  // padding: 0 20px;
}

.chart-container {
  width: 100%;
  height: 180px;
}

.data-panels {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.panel-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: linear-gradient(135deg, rgba(46, 255, 212, 0.1) 0%, rgba(46, 255, 212, 0.05) 100%);
  border: 1px solid rgba(46, 255, 212, 0.3);
  border-radius: 6px;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 50%, rgba(46, 255, 212, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 50%, rgba(46, 255, 212, 0.05) 0%, transparent 50%);
    border-radius: 6px;
    pointer-events: none;
  }
}

.panel-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.icon-container {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dotted rgba(46, 255, 212, 0.6);
  border-radius: 4px;
  padding: 2px;
}

.panel-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.panel-label {
  color: #2EFFD4;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.panel-right {
  display: flex;
  gap: 16px;
}

.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.data-value {
  color: #2EFFD4;
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
}

.data-label {
  color: #2EFFD4;
  font-size: 10px;
  line-height: 1;
  opacity: 0.9;
}
</style>
