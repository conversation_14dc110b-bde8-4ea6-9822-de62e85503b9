<!-- 行情咨询 -->
<template>
  <CPanel>
    <template #header>行情咨询</template>
    <template #content>
      <div class="market-cards">
        <div class="market-card">
          <div class="card-icon">
            <div class="icon-bg">
              <img src="@/assets/img/活牛价格.png" alt="活牛价格" class="icon-img" />
            </div>
          </div>
          <div class="card-label">活牛价格</div>
          <div class="card-value">4256</div>
        </div>
        
        <div class="market-card">
          <div class="card-icon">
            <div class="icon-bg">
              <img src="@/assets/img/私聊原料.png" alt="饲料原料" class="icon-img" />
            </div>
          </div>
          <div class="card-label">饲料原料</div>
          <div class="card-value">2250</div>
        </div>
        
        <div class="market-card">
          <div class="card-icon">
            <div class="icon-bg">
              <img src="@/assets/img/农业新闻.png" alt="农业新闻" class="icon-img" />
            </div>
          </div>
          <div class="card-label">农业新闻</div>
          <div class="card-value">852</div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import CPanel from '@/components/common/CPanel.vue'
</script>

<style lang="scss" scoped>
.market-cards {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  gap: 16px;
}

.market-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  position: relative;
  
  .card-icon {
    margin-bottom: 8px;
    
    .icon-bg {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: linear-gradient(135deg, #006265 0%, #006265 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: linear-gradient(135deg, #00DDFF, #006265);
        z-index: -1;
        opacity: 0.8;
      }
      
      &::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        bottom: 2px;
        border-radius: 50%;
        background: linear-gradient(135deg, #006265 0%, #006265 100%);
        z-index: -1;
      }
    }
    
    .icon-stack {
      width: 20px;
      height: 24px;
      background: #00DDFF;
      border-radius: 2px;
      position: relative;
      
      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 24px;
        background: #00DDFF;
        border-radius: 2px;
      }
      
      &::before {
        top: -4px;
        left: 2px;
        opacity: 0.8;
      }
      
      &::after {
        top: -8px;
        left: 4px;
        opacity: 0.6;
      }
    }
    
    .icon-bag {
      width: 24px;
      height: 20px;
      background: #00DDFF;
      border-radius: 12px 12px 4px 4px;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 8px;
        height: 4px;
        background: #00DDFF;
        border-radius: 4px 4px 0 0;
      }
    }
    
    .icon-globe {
      width: 24px;
      height: 24px;
      border: 2px solid #00DDFF;
      border-radius: 50%;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 16px;
        height: 16px;
        border: 1px solid #00DDFF;
        border-radius: 50%;
      }
      
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: #00DDFF;
      }
    }
  }
  
  .card-label {
    font-size: 14px;
    color: #C5D6E6;
    margin-bottom: 8px;
    text-align: center;
  }
  
  .card-value {
    font-size: 24px;
    font-weight: bold;
    color: #FFFFFF;
    text-align: center;
  }
}

// 小背景框的颜色样式
.market-card {
  background: linear-gradient(135deg, #1F786A 0%, #006265 100%);
  border-radius: 8px;
  padding: 16px 12px;
  box-shadow: 0 4px 20px rgba(0, 98, 101, 0.3);
  border: 0.5pt solid #006265;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 8px;
    background: linear-gradient(135deg, #1F786A 0%, #006265 100%);
    opacity: 0.1;
    z-index: -1;
  }
}
</style>
