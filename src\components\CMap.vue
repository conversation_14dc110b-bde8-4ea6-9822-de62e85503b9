<!--  中间地图部分 -->
<template>
  <div class="map" id="map" @mousemove="handleMouseMove">
    <div class="map-content">
      <CEcharts :option="mapOption" @onload="handleChartLoad" />
    </div>

    <!-- 农场信息悬浮提示 -->
    <FarmModal
      :visible="tooltipVisible"
      :farmData="selectedFarmData"
      :position="mousePosition"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getMapOption } from '@/modules/echartMap'
import CEcharts from './common/CEcharts.vue'
import FarmModal from './common/FarmModal.vue'

const mapOption = ref(getMapOption())
const tooltipVisible = ref(false)
const selectedFarmData = ref(null)
const mousePosition = ref({ x: 0, y: 0 })
let chartInstance: any = null

// 处理图表加载完成
const handleChartLoad = (chart: any) => {
  console.log('Chart loaded:', chart)
  chartInstance = chart

  // 监听鼠标悬浮事件
  chart.on('mouseover', (params: any) => {
    console.log('Mouseover event:', params)
    if (params.seriesType === 'scatter' && params.data && params.data.farmData) {
      console.log('Showing tooltip for:', params.data.farmData)
      selectedFarmData.value = params.data.farmData
      tooltipVisible.value = true
    }
  })

  // 监听鼠标离开事件
  chart.on('mouseout', (params: any) => {
    console.log('Mouseout event:', params)
    if (params.seriesType === 'scatter') {
      tooltipVisible.value = false
      selectedFarmData.value = null
    }
  })
}

// 处理鼠标移动，更新提示框位置
const handleMouseMove = (event: MouseEvent) => {
  mousePosition.value = {
    x: event.clientX + 10,
    y: event.clientY - 10
  }
}
</script>

<style lang="scss" scoped>
.map {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .map-content {
    position: relative;
    width: 1920px;
    height: 1080px;
  }
}
</style>
