<!-- 生产指标 -->
<template>
  <CPanel>
    <template #header>生产指标</template>
    <template #content>
      <div class="production-panel">
        <!-- 配种数据 -->
        <div class="data-item">
          <div class="icon-wrapper">
            <img src="@/assets/img/生产icon1.png" alt="配种图标" class="icon" />
          </div>
          <div class="data-content">
            <div class="main-value">42563</div>
            <div class="label">配种数</div>
          </div>
          <div class="separator"></div>
          <div class="data-content">
            <div class="main-value">64%</div>
            <div class="label">受胎率</div>
          </div>
        </div>
        
        <!-- 产犊数据 -->
        <div class="data-item">
          <div class="icon-wrapper">
            <img src="@/assets/img/生产icon2.png" alt="产犊图标" class="icon" />
          </div>
          <div class="data-content">
            <div class="main-value">3851</div>
            <div class="label">产犊数</div>
          </div>
          <div class="separator"></div>
          <div class="data-content">
            <div class="main-value">82%</div>
            <div class="label">产犊率</div>
          </div>
          <div class="separator"></div>
          <div class="data-content">
            <div class="main-value">64%</div>
            <div class="label">犊牛成活率</div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import CPanel from '@/components/common/CPanel.vue'
</script>

<style lang="scss" scoped>
.production-panel {
  width: 100%;
  height: 100%;
  background: url('@/assets/img/生产bg.png') no-repeat center center;
  background-size: cover;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.icon-wrapper {
  position: relative;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  width: 40px;
  height: 40px;
  filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.5));
}

.data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
}

.main-value {
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
  margin-bottom: 5px;
}

.label {
  font-size: 12px;
  color: #00ffff;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.6);
}

.separator {
  width: 1px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, #00ffff, transparent);
  margin: 0 10px;
}
</style>
