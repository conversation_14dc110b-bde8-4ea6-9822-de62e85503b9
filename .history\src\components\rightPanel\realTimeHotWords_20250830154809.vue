<!-- 山东实时热词  -->
<template>
  <CPanel>
    <template #header>防疫落实</template>
    <template #content>
      <div class="dashboard">
        <!-- 左侧圆形进度条 -->
        <div class="progress-section">
          <div class="progress-circle">
            <svg class="progress-svg" viewBox="0 0 120 120">
              <!-- 背景圆环 -->
              <circle
                cx="60"
                cy="60"
                r="50"
                fill="none"
                stroke="rgba(255, 255, 255, 0.1)"
                stroke-width="8"
              />
              <!-- 进度圆环 -->
              <circle
                cx="60"
                cy="60"
                r="50"
                fill="none"
                stroke="url(#gradient)"
                stroke-width="8"
                stroke-linecap="round"
                :stroke-dasharray="`${circumference} ${circumference}`"
                :stroke-dashoffset="strokeDashoffset"
                transform="rotate(-90 60 60)"
              />
            </svg>
            <!-- 渐变定义 -->
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stop-color="#2EFFD4" />
                <stop offset="100%" stop-color="#A8FE6C" />
              </linearGradient>
            </defs>
            <!-- 中心文字 -->
            <div class="progress-text">
              <span class="percentage">{{ progressValue }}%</span>
            </div>
          </div>
          <div class="progress-label">防疫落实</div>
        </div>
        
        <!-- 右侧数据列表 -->
        <div class="data-section">
          <div class="data-item" v-for="(item, index) in diseaseData" :key="index">
            <div class="item-bullet"></div>
            <div class="item-content">
              <span class="item-name">{{ item.name }}</span>
              <span class="item-value">{{ item.value }}</span>
              <span class="item-unit">头</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import CPanel from '@/components/common/CPanel.vue'

// 进度值
const progressValue = ref(92.5)

// 计算圆环周长和偏移量
const radius = 50
const circumference = 2 * Math.PI * radius
const strokeDashoffset = computed(() => {
  return circumference - (progressValue.value / 100) * circumference
})

// 疾病数据
const diseaseData = ref([
  { name: '疫病名称1', value: 256 },
  { name: '疫病名称2', value: 137 },
  { name: '疫病名称3', value: 87 },
  { name: '疫病名称4', value: 56 },
  { name: '疫病名称5', value: 24 }
])

onMounted(() => {
  // 可以在这里添加数据获取逻辑
})
</script>

<style lang="scss" scoped>
::v-deep .panel-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard {
  position: relative;
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg, #1a2a3a 0%, #2d4a5a 100%);
  box-sizing: border-box;
  display: flex;
  padding: 20px;
  gap: 20px;
  align-items: center;
}

.progress-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.progress-circle {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.progress-text {
  position: relative;
  z-index: 2;
  text-align: center;
}

.percentage {
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(135deg, #2EFFD4 0%, #A8FE6C 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.progress-label {
  color: white;
  font-size: 14px;
  text-align: center;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 1px;
    background: repeating-linear-gradient(
      to right,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.3) 2px,
      rgba(255, 255, 255, 0.3) 4px
    );
  }
}

.data-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-bullet {
  width: 8px;
  height: 8px;
  background: #A8FE6C;
  border-radius: 2px;
  flex-shrink: 0;
}

.item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.item-name {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.item-value {
  font-size: 16px;
  font-weight: bold;
  color: #A8FE6C;
}

.item-unit {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}
</style>
