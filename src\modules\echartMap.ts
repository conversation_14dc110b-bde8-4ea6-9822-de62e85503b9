import * as echarts from 'echarts'
import ynData from '@/assets/data/永宁县'
// 导入地图背景图片
import mapBg from '@/assets/img/ditu.png'  // 原始地图背景
// 如果有多张拼接图片，可以取消下面的注释并注释掉上面的导入
// import mapBg1 from '@/assets/img/ditu1.png'  // 地图区域1
// import mapBg2 from '@/assets/img/ditu2.png'  // 地图区域2
// import mapBg3 from '@/assets/img/ditu3.png'  // 地图区域3
// import mapBg4 from '@/assets/img/ditu4.png'  // 地图区域4
// 地图打点图标
import mapIcon1 from '@/assets/img/mapIcon1.png'
import mapIcon2 from '@/assets/img/mapIcon2.png'
import mapIcon3 from '@/assets/img/mapIcon3.png'
echarts.registerMap('yn', ynData as any)
const mapIconList: any = [mapIcon1, mapIcon2, mapIcon3]

// 地图背景配置 - 使用单张图片创建多层效果
const mapBackgrounds = {
  bg1: mapBg, // 基础层
  bg2: mapBg, // 纹理层
  bg3: mapBg, // 高光层
  bg4: mapBg  // 装饰层
}

// 如果要使用多张拼接图片，请将上面的配置替换为：
// const mapBackgrounds = {
//   bg1: require('@/assets/img/ditu1.png'), // 基础纹理图片
//   bg2: require('@/assets/img/ditu2.png'), // 细节纹理图片
//   bg3: require('@/assets/img/ditu3.png'), // 高光效果图片
//   bg4: require('@/assets/img/ditu4.png')  // 装饰效果图片
// }



// 获取地图配置
export const getMapOption = () => {
  // 渐变层颜色 - 青绿色调
  const colorList: string[] = [
    '#2a6b5c',
    '#266258',
    '#225954',
    '#1e5050',
    '#1a474c',
    '#163e48',
    '#123544',
    '#0e2c40',
    '#0a233c',
    '#061a38',
    '#021134',
    '#000830'
  ]
  // 生成渐变图层
  const geoList: any = []
  for (let i = 1; i <= colorList.length; i++) {
    const mapOption: any = {
      map: 'yn',
      aspectScale: 0.85,
      emphasis: {
        disabled: true
      },
      z: 12 - i,
      layoutCenter: ['50%', `${i * 0.3 + 50}%`], //地图位置
      layoutSize: '85%',
      itemStyle: {
        normal: {
          areaColor: colorList[i - 1],
          borderWidth: 0
        }
      }
    }
    if (i === colorList.length) {
      mapOption.itemStyle.normal.shadowColor = 'rgba(0, 0, 0, 0.71)'
      mapOption.itemStyle.normal.shadowBlur = 100
    }
    geoList.push(mapOption)
  }
  // 获取打点数据配置
  const pointSeriesData = getPointData()
  const option = {
    tooltip: {
      show: false  // 禁用全局tooltip
    },
    geo: [
      // 最外围发光边界
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'], //地图位置
        layoutSize: '85%',
        z: 12,
        emphasis: {
          disabled: true
        },
        itemStyle: {
          normal: {
            borderColor: '#00CED1',
            borderWidth: 8,
            shadowColor: 'rgba(0, 206, 209, 0.4)',
            shadowBlur: 20
          }
        }
      },
      // 最外层遮罩蒙版
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'], //地图位置
        layoutSize: '85%',
        z: 14,
        itemStyle: {
          normal: {
            areaColor: 'rgba(106, 125, 171, 0.45)',
            borderWidth: 0
          }
        },
        emphasis: {
          itemStyle: {
            areaColor: '#00E2B4',
            borderWidth: 2,
            borderColor: '#00E2B4',
            shadowBlur: 10,
            shadowColor: 'rgba(0, 226, 180, 0.5)'
          }
        },
        label: {
          show: true,
          color: '#fff',
          fontSize: 14
        }
      },
      // 地图背景拼接层1 - 基础纹理层
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'],
        layoutSize: '85%',
        z: 10,
        emphasis: {
          disabled: true
        },
        itemStyle: {
          normal: {
            areaColor: {
              image: mapBackgrounds.bg1,
              repeat: 'no-repeat'
            },
            borderWidth: 0,
            opacity: 0.6
          }
        }
      },
      // 地图背景拼接层2 - 细节纹理层
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'],
        layoutSize: '85%',
        z: 10.1,
        emphasis: {
          disabled: true
        },
        itemStyle: {
          normal: {
            areaColor: {
              image: mapBackgrounds.bg2,
              repeat: 'no-repeat'
            },
            borderWidth: 0,
            opacity: 0.4
          }
        }
      },
      // 地图背景拼接层3 - 高光层
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'],
        layoutSize: '85%',
        z: 10.2,
        emphasis: {
          disabled: true
        },
        itemStyle: {
          normal: {
            areaColor: {
              image: mapBackgrounds.bg3,
              repeat: 'no-repeat'
            },
            borderWidth: 0,
            opacity: 0.3
          }
        }
      },
      // 地图背景拼接层4 - 装饰层
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'],
        layoutSize: '85%',
        z: 10.3,
        emphasis: {
          disabled: true
        },
        itemStyle: {
          normal: {
            areaColor: {
              image: mapBackgrounds.bg4,
              repeat: 'no-repeat'
            },
            borderWidth: 0,
            opacity: 0.2
          }
        }
      },
      // 主要边界层
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'], //地图位置
        layoutSize: '85%',
        z: 12,
        emphasis: {
          disabled: true
        },
        itemStyle: {
          normal: {
            areaColor: 'transparent', // 设为透明，让下面的拼接图片显示
            borderColor: '#1F786A',
            borderWidth: 1
          }
        }
      },
      ...geoList
    ],
    series: [
      // 地图打点数据
      ...pointSeriesData
    ]
  }
  return option
}

// 生成地图打点数据
const getPointData = () => {
  const districtData: {
    name: string
    value: number
    point: number[]
    type: number // 图标类型 0,1,2 对应三种不同图标
    address: string
    details: Array<{
      time: string
      level: string
    }>
    image?: string
  }[] = [
    {
      name: '张三养殖场',
      value: 300,
      point: [106.253781, 38.28043],
      type: 0,
      address: '永宁县闽宁镇黄大组23号',
      details: [
        { time: '2025-8-28 19:22:39', level: '一级/二级' },
        { time: '2025-8-28 19:22:39', level: '一级/二级' },
        { time: '2025-8-28 19:22:39', level: '一级/二级' }
      ],
      image: '/src/assets/img/牛.png'
    },
    {
      name: '李四农场',
      value: 200,
      point: [106.15, 38.35],
      type: 1,
      address: '永宁县李俊镇新村15号',
      details: [
        { time: '2025-8-28 18:15:22', level: '二级/三级' },
        { time: '2025-8-28 17:30:15', level: '一级/二级' }
      ],
      image: '/src/assets/img/猪.png'
    },
    {
      name: '王五牧场',
      value: 129,
      point: [106.35, 38.25],
      type: 2,
      address: '永宁县闽宁镇东村8号',
      details: [
        { time: '2025-8-28 20:10:33', level: '三级/四级' },
        { time: '2025-8-28 19:45:18', level: '二级/三级' },
        { time: '2025-8-28 18:20:45', level: '一级/二级' }
      ],
      image: '/src/assets/img/羊.png'
    },
    {
      name: '赵六养殖基地',
      value: 107,
      point: [106.20, 38.20],
      type: 0,
      address: '永宁县望洪镇南街12号',
      details: [
        { time: '2025-8-28 16:55:28', level: '一级/二级' },
        { time: '2025-8-28 15:30:12', level: '二级/三级' }
      ],
      image: '/src/assets/img/牛.png'
    },
    {
      name: '孙七农业园',
      value: 86,
      point: [106.30, 38.30],
      type: 1,
      address: '永宁县杨和镇北区5号',
      details: [
        { time: '2025-8-28 21:05:15', level: '一级/二级' }
      ],
      image: '/src/assets/img/猪.png'
    },
    {
      name: '周八牧业',
      value: 95,
      point: [106.18, 38.32],
      type: 2,
      address: '永宁县胜利乡西村20号',
      details: [
        { time: '2025-8-28 14:20:35', level: '二级/三级' },
        { time: '2025-8-28 13:15:22', level: '一级/二级' }
      ],
      image: '/src/assets/img/羊.png'
    },
    {
      name: '吴九养殖场',
      value: 78,
      point: [106.28, 38.18],
      type: 0,
      address: '永宁县望远镇中心街18号',
      details: [
        { time: '2025-8-28 12:45:18', level: '三级/四级' }
      ],
      image: '/src/assets/img/牛.png'
    },
    {
      name: '郑十农场',
      value: 156,
      point: [106.08, 38.28],
      type: 1,
      address: '永宁县红寺堡镇东街25号',
      details: [
        { time: '2025-8-28 11:30:45', level: '一级/二级' },
        { time: '2025-8-28 10:15:33', level: '二级/三级' }
      ],
      image: '/src/assets/img/猪.png'
    }
  ]
  
  const pointSeriesData: any = []
  
  districtData.forEach((item: any) => {
    // 地图打点图标
    const mapPoint = {
      type: 'scatter',
      coordinateSystem: 'geo',
      geoIndex: 0,
      zlevel: 5,
      label: {
        show: false
      },
      symbol: `image://` + mapIconList[item.type],
      symbolSize: [40, 40],
      symbolOffset: [0, 0],
      z: 999,
      data: [
        {
          name: item.name,
          value: [item.point[0], item.point[1], item.value],
          itemStyle: {
            color: 'transparent'
          },
          // 添加完整的数据信息用于点击事件
          farmData: {
            name: item.name,
            address: item.address,
            details: item.details,
            image: item.image,
            value: item.value
          }
        }
      ],
      tooltip: {
        show: false  // 禁用默认tooltip，使用自定义悬浮框
      }
    }
    
    // 底部光圈效果
    const rippleEffect = {
      type: 'effectScatter',
      coordinateSystem: 'geo',
      geoIndex: 0,
      zlevel: 4,
      showEffectOn: 'render',
      rippleEffect: {
        scale: 3,
        brushType: 'stroke'
      },
      symbol: 'circle',
      symbolSize: [15, 15],
      itemStyle: {
        color: 'rgba(0, 255, 255, 0.8)',
        shadowBlur: 10,
        shadowColor: 'rgba(0, 255, 255, 0.5)'
      },
      data: [
        {
          name: item.name,
          value: [item.point[0], item.point[1], item.value]
        }
      ]
    }
    
    pointSeriesData.push(mapPoint)
    pointSeriesData.push(rippleEffect)
  })
  
  return pointSeriesData
}


