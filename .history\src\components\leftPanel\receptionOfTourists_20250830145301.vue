<!-- 行情咨询 -->
<template>
  <CPanel>
    <template #header>行情咨询</template>
    <template #content>
      <div class="market-cards">
        <div class="market-card">
          <div class="card-icon">
            <img src="@/assets/img/活牛价格.png" alt="活牛价格" />
          </div>
          <div class="card-label">活牛价格</div>
          <div class="card-value">4256</div>
        </div>
        
        <div class="market-card">
          <div class="card-icon">
            <img src="@/assets/img/私聊原料.png" alt="饲料原料" />
          </div>
          <div class="card-label">饲料原料</div>
          <div class="card-value">2250</div>
        </div>
        
        <div class="market-card">
          <div class="card-icon">
            <img src="@/assets/img/农业新闻.png" alt="农业新闻" />
          </div>
          <div class="card-label">农业新闻</div>
          <div class="card-value">852</div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import CPanel from '@/components/common/CPanel.vue'
</script>

<style lang="scss" scoped>
.market-cards {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  gap: 16px;
}

.market-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  position: relative;
  
  .card-icon {
    margin-bottom: 8px;
  }
  
  .card-label {
    font-size: 14px;
    color: #C5D6E6;
    margin-bottom: 8px;
    text-align: center;
  }
  
  .card-value {
    font-size: 14px;
    font-weight: bold;
    color: #FFFFFF;
    text-align: center;
  }
}

// 小背景框的颜色样式
.market-card {
  background: linear-gradient(135deg, #1F786A 0%, #006265 100%);
  border-radius: 8px;
  padding: 16px 12px;
  box-shadow: 0 4px 20px rgba(0, 98, 101, 0.3);
  border: 0.5pt solid #006265;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 8px;
    background: linear-gradient(135deg, #1F786A 0%, #006265 100%);
    opacity: 0.1;
    z-index: -1;
  }
}
</style>
