<!-- 山东实时热词  -->
<template>
  <CPanel>
    <template #header>防疫落实</template>
    <template #content>
      <div class="dashboard">
        <!-- 左侧仪表盘 -->
        <div class="gauge-section">
          <CEcharts :option="option" />
          <div class="gauge-label">防疫落实</div>
        </div>
        
        <!-- 右侧数据列表 -->
        <div class="data-section">
          <div class="data-item" v-for="(item, index) in diseaseData" :key="index">
            <div class="item-bullet"></div>
            <div class="item-content">
              <span class="item-name">{{ item.name }}</span>
              <span class="item-value">{{ item.value }}</span>
              <span class="item-unit">头</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref<any>({})

// 疾病数据
const diseaseData = ref([
  { name: '疫病名称1', value: 256 },
  { name: '疫病名称2', value: 137 },
  { name: '疫病名称3', value: 87 },
  { name: '疫病名称4', value: 56 },
  { name: '疫病名称5', value: 24 }
])

const initEcharts = () => {
  return {
    tooltip: {
      formatter: "{a} <br/>{b} : {c}%"
    },
    toolbox: {
      feature: {
        restore: {},
        saveAsImage: {}
      }
    },
    series: [
      {
        type: 'gauge',
        name: '防疫落实',
        radius: '60%',
        startAngle: '215',
        endAngle: '-35',
        splitNumber: '100',
        pointer: {
          show: false
        },
        detail: {
          offsetCenter: [0, -20],
          formatter: '{value}%',
          fontSize: 24,
          fontWeight: 'bold',
          color: '#A8FE6C'
        },
        data: [{ value: 92.5, name: '防疫落实' }],
        axisLine: {
          show: true,
          lineStyle: {
            color: [
              [0, '#2EFFD4'], 
              [0.925, '#A8FE6C'], 
              [1, '#ddd']
            ],
            width: 25,
            shadowBlur: 15,
            shadowColor: '#2EFFD4',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            opacity: 1
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          length: 25,
          lineStyle: {
            color: '#eee',
            width: 2,
            type: 'solid',
            shadowColor: '#eee',
            shadowBlur: 25,
            shadowOffsetX: 10,
            shadowOffsetY: 10,
            opacity: 0.5
          }
        },
        axisLabel: {
          show: false
        }
      }
    ]
  }
}

onMounted(() => {
  option.value = initEcharts()
})
</script>

<style lang="scss" scoped>
::v-deep .panel-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard {
  position: relative;
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg, #1a2a3a 0%, #2d4a5a 100%);
  box-sizing: border-box;
  display: flex;
  padding: 20px;
  gap: 20px;
  align-items: center;
}

.gauge-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 200px;
  height: 160px;
}

.gauge-label {
  color: white;
  font-size: 14px;
  text-align: center;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 1px;
    background: repeating-linear-gradient(
      to right,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.3) 2px,
      rgba(255, 255, 255, 0.3) 4px
    );
  }
}

.data-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-bullet {
  width: 8px;
  height: 8px;
  background: #A8FE6C;
  border-radius: 2px;
  flex-shrink: 0;
}

.item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.item-name {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.item-value {
  font-size: 16px;
  font-weight: bold;
  color: #A8FE6C;
}

.item-unit {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}
</style>
