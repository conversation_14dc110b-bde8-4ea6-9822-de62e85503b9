# 地图多图片拼接制作指南

## 已完成的修改

✅ 已经修改了 `src/modules/echartMap.ts` 文件，支持多张图片拼接
✅ 创建了4个图层的叠加效果
✅ 设置了不同的透明度和层级

## 当前效果

目前使用单张 `ditu.png` 图片创建了4个不同透明度的图层：
- 图层1：透明度 60% (基础纹理层)
- 图层2：透明度 40% (细节纹理层)  
- 图层3：透明度 30% (高光层)
- 图层4：透明度 20% (装饰层)

这样可以创造出丰富的视觉层次效果。

## 如何制作真正的拼接图片

### 步骤1：准备工具
- 推荐使用 Photoshop、GIMP 或在线工具如 Canva
- 需要原始地图图片 `ditu.png`

### 步骤2：分层设计思路

#### ditu1.png - 基础纹理层
```
内容：地形基础色彩、主要区域划分
特点：提供地图的基本轮廓和色调
建议：使用较深的基础色，如深绿、深蓝等
```

#### ditu2.png - 细节纹理层
```
内容：道路网络、河流、重要建筑轮廓
特点：增加地图的细节信息
建议：使用中等亮度的颜色，突出重要路径
```

#### ditu3.png - 高光层
```
内容：重点区域高光、山脉阴影、水面反光
特点：增加立体感和重点标识
建议：使用亮色高光，如白色、浅蓝色
```

#### ditu4.png - 装饰层
```
内容：边框装饰、特效光晕、图案纹理
特点：增加视觉美感和科技感
建议：使用发光效果、渐变边框
```

### 步骤3：制作流程

1. **打开原始图片**
   - 在图像编辑软件中打开 `ditu.png`
   - 复制为4个图层

2. **制作基础纹理层 (ditu1.png)**
   ```
   - 调整整体色调为深色
   - 保留主要地形轮廓
   - 删除细节元素
   - 保存为 PNG 格式
   ```

3. **制作细节纹理层 (ditu2.png)**
   ```
   - 保留道路、河流等线性元素
   - 其他区域设为透明
   - 调整线条颜色为中等亮度
   - 保存为 PNG 格式
   ```

4. **制作高光层 (ditu3.png)**
   ```
   - 只保留需要突出的区域
   - 添加高光效果
   - 其他区域设为透明
   - 使用亮色调
   - 保存为 PNG 格式
   ```

5. **制作装饰层 (ditu4.png)**
   ```
   - 添加边框装饰
   - 创建发光效果
   - 添加纹理图案
   - 其他区域设为透明
   - 保存为 PNG 格式
   ```

### 步骤4：应用到项目

1. **放置图片文件**
   ```
   将制作好的4张图片放到：
   src/assets/img/ditu1.png
   src/assets/img/ditu2.png
   src/assets/img/ditu3.png
   src/assets/img/ditu4.png
   ```

2. **修改代码配置**
   ```typescript
   // 在 src/modules/echartMap.ts 中取消注释并修改：
   const mapBackgrounds = {
     bg1: require('@/assets/img/ditu1.png'),
     bg2: require('@/assets/img/ditu2.png'), 
     bg3: require('@/assets/img/ditu3.png'),
     bg4: require('@/assets/img/ditu4.png')
   }
   ```

## 快速测试方案

如果暂时没有时间制作4张不同的图片，可以：

1. **复制现有图片**
   ```bash
   # 在 src/assets/img/ 目录下
   copy ditu.png ditu1.png
   copy ditu.png ditu2.png
   copy ditu.png ditu3.png
   copy ditu.png ditu4.png
   ```

2. **简单处理**
   - ditu1.png: 保持原样
   - ditu2.png: 调整亮度 +20%
   - ditu3.png: 调整对比度 +30%
   - ditu4.png: 添加模糊效果

## 预期效果

使用多张拼接图片后，地图将呈现：
- 🎨 **丰富的视觉层次**：多层叠加创造深度感
- ✨ **自然的过渡效果**：不同透明度实现平滑融合
- 🔍 **突出的重点区域**：通过高光层强调重要位置
- 🎯 **专业的视觉效果**：科技感和现代感并存

## 注意事项

- 确保所有图片尺寸一致
- 使用 PNG 格式支持透明背景
- 控制文件大小，建议单个文件不超过 2MB
- 测试在不同设备上的显示效果
