# 地图多图片拼接使用说明

## 概述
已经修改了地图配置，支持使用多张图片进行拼接，让地图显示效果更加丰富和美观。

## 图片文件要求

需要在 `src/assets/img/` 目录下准备以下4张图片：

1. **ditu1.png** - 基础纹理层
   - 作用：提供地图的基础纹理和颜色
   - 透明度：60%
   - 建议：使用地形纹理、基础色彩

2. **ditu2.png** - 细节纹理层  
   - 作用：添加地图的细节纹理
   - 透明度：40%
   - 建议：使用道路、河流、建筑等细节元素

3. **ditu3.png** - 高光层
   - 作用：添加高光效果和重点区域标识
   - 透明度：30%
   - 建议：使用高光、阴影、重点区域标记

4. **ditu4.png** - 装饰层
   - 作用：添加装饰元素和特效
   - 透明度：20%
   - 建议：使用边框装饰、特效元素

## 图片制作建议

### 尺寸要求
- 建议使用相同尺寸的图片（如 1024x768 或更高分辨率）
- 保持图片比例与地图区域一致

### 设计原则
1. **分层设计**：每张图片负责不同的视觉层次
2. **透明背景**：除了需要显示的内容，其他区域使用透明背景
3. **颜色协调**：确保多层叠加后颜色和谐
4. **细节丰富**：利用多层效果增加地图的视觉深度

### 制作流程
1. 准备原始地图图片
2. 使用图像编辑软件（如 Photoshop、GIMP）分层处理
3. 将不同元素分别保存为独立的 PNG 文件
4. 确保每个文件都有透明背景

## 效果说明

修改后的地图将具有以下效果：
- **层次丰富**：通过4层图片叠加，创造丰富的视觉层次
- **自然过渡**：不同透明度的图层自然融合
- **细节突出**：多层纹理让地图更加生动
- **视觉深度**：通过层次叠加增加地图的立体感

## 当前配置

```typescript
// 4个图层的配置
- 图层1 (z: 10.0, opacity: 0.6) - 基础纹理
- 图层2 (z: 10.1, opacity: 0.4) - 细节纹理  
- 图层3 (z: 10.2, opacity: 0.3) - 高光层
- 图层4 (z: 10.3, opacity: 0.2) - 装饰层
- 边界层 (z: 12) - 地图边界和交互
```

## 自定义调整

如需调整效果，可以修改 `src/modules/echartMap.ts` 中的以下参数：

1. **透明度调整**：修改各层的 `opacity` 值
2. **层级调整**：修改各层的 `z` 值
3. **位置微调**：修改 `layoutCenter` 参数
4. **尺寸调整**：修改 `layoutSize` 参数

## 注意事项

1. 确保所有图片文件都存在，否则会显示错误
2. 图片文件大小不宜过大，建议单个文件不超过 2MB
3. 使用 PNG 格式以支持透明背景
4. 测试不同设备上的显示效果
