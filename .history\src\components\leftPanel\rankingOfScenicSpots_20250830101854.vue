<!-- 技术课堂 -->
<template>
  <CPanel>
    <template #header>技术课堂</template>
    <template #content>
      <div class="content">
        <div class="left-section">
          <div class="center-circle">
            <div class="total-number">256</div>
          </div>
          <div class="chart-container" id="technical-chart"></div>
        </div>

      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'

const statsList = ref([
  { name: '主题1', percent: 34, color: '#E83F46' },
  { name: '主题5', percent: 34, color: '#0DC869' },
  { name: '主题2', percent: 30, color: '#FF8D39' },
  { name: '主题6', percent: 30, color: '#00D4DC' },
  { name: '主题3', percent: 34, color: '#FFD339' },
  { name: '主题7', percent: 34, color: '#4F7DFF' },
  { name: '主题4', percent: 30, color: '#B4E61A' },
  { name: '主题8', percent: 30, color: '#AF48FF' }
])

const initChart = () => {
  const chartDom = document.getElementById('technical-chart')
  if (chartDom) {
    // 清除之前的图表实例
    echarts.dispose(chartDom)
    const myChart = echarts.init(chartDom)
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          return params.seriesName + "</br>" +
            params.marker + "" + params.data.name + "</br>" +
            "数量：" + params.data.value + "</br>" +
            "占比：" + params.percent + "%"
        }
      },
      legend: {
        type: "scroll",
        orient: 'vertical',
        left: '65%',
        align: 'left',
        top: 'middle',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: 12
        },
        height: 150,
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 8
      },
      series: [{
        name: '技术课堂',
        type: 'pie',
        radius: ['30%', '65%'],
        center: ['32%', '50%'],
        clockwise: false,
        avoidLabelOverlap: false,
        data: statsList.value.map(item => ({
          value: item.percent,
          name: item.name,
          itemStyle: { color: item.color }
        })),
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    myChart.setOption(option, true) // 强制重新渲染
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss" scoped>
.content {
  height: 180px;
  position: relative;
  padding: 10px;
}

.left-section {
  position: relative;
  width: 100%;
  height: 100%;

  .center-circle {
    position: absolute;
    z-index: 2;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;

    .total-number {
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      line-height: 1;
    }
  }
}



.chart-container {
  width: 100%;
  height: 100%;
}
</style>
